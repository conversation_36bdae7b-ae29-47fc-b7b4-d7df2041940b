# Backend API - Sistema de Empleados Coppel

Backend desarrollado en Node.js + Express + PostgreSQL para el sistema de gestión de empleados.

## 🚀 Características

- **CRUD completo** de empleados
- **Cálculos automáticos** de vacaciones y aguinaldo
- **Procedimientos almacenados** en PostgreSQL
- **API RESTful** con validaciones
- **Manejo de errores** robusto
- **CORS configurado** para Angular

## 📋 Endpoints Disponibles

### Empleados Básicos
- `GET /api/empleados` - Obtener todos los empleados
- `GET /api/empleados/:id` - Obtener empleado por ID
- `POST /api/empleados` - Crear nuevo empleado
- `PUT /api/empleados/:id` - Actualizar empleado
- `DELETE /api/empleados/:id` - Eliminar empleado

### Empleados con Detalles (Vacaciones y Aguinaldo)
- `GET /api/empleados/:id/detalles` - Obtener empleado con cálculos
- `GET /api/empleados/detalles/todos` - Obtener todos con cálculos

### Utilidades
- `GET /health` - Estado del servidor
- `GET /` - Información de la API

## 🛠️ Instalación y Configuración

### 1. Instalar dependencias
```bash
cd backend
npm install
```

### 2. Configurar variables de entorno
```bash
cp .env.example .env
```

Editar `.env` con tus datos de PostgreSQL:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=empleados_coppel
DB_USER=postgres
DB_PASSWORD=tu_password
PORT=3000
NODE_ENV=development
```

### 3. Ejecutar el servidor
```bash
# Desarrollo (con nodemon)
npm run dev

# Producción
npm start
```

## 📊 Estructura del Proyecto

```
backend/
├── config/
│   └── database.js          # Configuración PostgreSQL
├── controllers/
│   └── empleadoController.js # Lógica de negocio
├── models/
│   └── empleadoModel.js      # Interacción con BD
├── routes/
│   └── empleadoRoutes.js     # Definición de rutas
├── middleware/
│   └── errorHandler.js       # Manejo de errores
├── .env.example              # Variables de entorno
├── package.json              # Dependencias
└── server.js                 # Servidor principal
```

## 🔧 Funciones PostgreSQL Utilizadas

El backend utiliza las siguientes funciones de PostgreSQL:

- `fn_crear_empleado()` - Crear empleado
- `fn_obtener_empleados()` - Obtener todos los empleados
- `fn_obtener_empleado_id()` - Obtener empleado por ID
- `fn_actualizar_empleado()` - Actualizar empleado
- `fn_eliminar_empleado()` - Eliminar empleado
- `fn_obtener_empleado_id_detalle()` - Empleado con cálculos
- `fn_obtener_todos_empleados_detalles()` - Todos con cálculos

## 📝 Ejemplo de Uso

### Crear Empleado
```bash
curl -X POST http://localhost:3000/api/empleados \
  -H "Content-Type: application/json" \
  -d '{
    "nombre": "Juan Carlos",
    "apellido_paterno": "Pérez",
    "apellido_materno": "García",
    "sueldo_base": 15000.00,
    "fecha_ingreso": "2020-01-15"
  }'
```

### Obtener Empleado con Detalles
```bash
curl http://localhost:3000/api/empleados/1/detalles
```

## 🛡️ Validaciones

- Todos los campos son requeridos
- Sueldo base debe ser mayor a 0
- Nombres se limpian de espacios extra
- Manejo de errores de base de datos

## 🔍 Testing

Para probar la API puedes usar:
- **Postman** o **Insomnia**
- **curl** desde terminal
- El frontend Angular (una vez desarrollado)

## 📞 Soporte

Si encuentras algún problema, verifica:
1. PostgreSQL esté corriendo
2. Las funciones estén creadas en la BD
3. Las variables de entorno estén correctas
4. El puerto 3000 esté disponible
