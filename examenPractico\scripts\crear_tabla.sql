-- Table: public.empleados

-- DROP TABLE IF EXISTS public.empleados;

CREATE TABLE IF NOT EXISTS public.empleados
(
    id integer NOT NULL DEFAULT nextval('empleados_id_seq'::regclass),
    nombre character varying(100) COLLATE pg_catalog."default",
    apellido_paterno character varying(100) COLLATE pg_catalog."default",
    apellido_materno character varying(100) COLLATE pg_catalog."default",
    sueldo_base numeric(10,2),
    fecha_ingreso date,
    CONSTRAINT empleados_pkey PRIMARY KEY (id)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.empleados
    OWNER to postgres;