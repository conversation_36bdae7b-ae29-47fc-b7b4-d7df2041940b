-- =====================================================
-- SCRIPT DE PRUEBAS PARA PROCEDIMIENTOS ALMACENADOS
-- =====================================================

-- <PERSON><PERSON>r da<PERSON> de prueba (opcional)
-- DELETE FROM empleados WHERE nombre LIKE 'Test%';

-- =====================================================
-- PRUEBAS DE INSERCIÓN
-- =====================================================

-- Insertar empleados de prueba con diferentes antigüedades
SELECT * FROM crear_empleado('<PERSON>', '<PERSON>', '<PERSON>', 15000.00, '2020-01-15');
SELECT * FROM crear_empleado('<PERSON> <PERSON>', 'López', 'Martínez', 18000.00, '2019-06-10');
SELECT * FROM crear_empleado('Pedro Luis', 'González', 'Hernández', 12000.00, '2023-03-20');
SELECT * FROM crear_empleado('Ana Sofía', 'Rodríguez', 'Jiménez', 20000.00, '2018-11-05');
SELECT * FROM crear_empleado('Carlos Alberto', 'Sánchez', 'Morales', 16500.00, '2021-08-12');

-- =====================================================
-- PRUEBAS DE CONSULTA
-- =====================================================

-- Obtener todos los empleados
SELECT 'TODOS LOS EMPLEADOS:' as titulo;
SELECT * FROM obtener_empleados();

-- Obtener empleado específico
SELECT 'EMPLEADO ESPECÍFICO (ID=1):' as titulo;
SELECT * FROM obtener_empleado_por_id(1);

-- =====================================================
-- PRUEBAS DE CÁLCULOS
-- =====================================================

-- Probar cálculo de vacaciones para diferentes fechas
SELECT 'PRUEBAS DE CÁLCULO DE VACACIONES:' as titulo;
SELECT 'Empleado nuevo (0 años)' as caso, calcular_dias_vacaciones('2024-01-01') as dias;
SELECT 'Empleado 1 año' as caso, calcular_dias_vacaciones('2023-01-01') as dias;
SELECT 'Empleado 2 años' as caso, calcular_dias_vacaciones('2022-01-01') as dias;
SELECT 'Empleado 3 años' as caso, calcular_dias_vacaciones('2021-01-01') as dias;
SELECT 'Empleado 4 años' as caso, calcular_dias_vacaciones('2020-01-01') as dias;
SELECT 'Empleado 5 años' as caso, calcular_dias_vacaciones('2019-01-01') as dias;
SELECT 'Empleado 6+ años' as caso, calcular_dias_vacaciones('2018-01-01') as dias;

-- Probar cálculo de aguinaldo
SELECT 'PRUEBAS DE CÁLCULO DE AGUINALDO:' as titulo;
SELECT 'Empleado nuevo (menos 6 meses)' as caso, calcular_aguinaldo('2024-01-01', 15000) as aguinaldo;
SELECT 'Empleado 6 meses' as caso, calcular_aguinaldo('2023-12-01', 15000) as aguinaldo;
SELECT 'Empleado 1 año' as caso, calcular_aguinaldo('2023-01-01', 15000) as aguinaldo;
SELECT 'Empleado 2+ años' as caso, calcular_aguinaldo('2022-01-01', 15000) as aguinaldo;

-- =====================================================
-- PRUEBAS DE DETALLES COMPLETOS
-- =====================================================

-- Obtener detalles completos de un empleado
SELECT 'DETALLES COMPLETOS DE EMPLEADO (ID=1):' as titulo;
SELECT * FROM obtener_empleado_detalles(1);

-- Obtener detalles completos de todos los empleados
SELECT 'DETALLES COMPLETOS DE TODOS LOS EMPLEADOS:' as titulo;
SELECT * FROM obtener_todos_empleados_detalles();

-- =====================================================
-- PRUEBAS DE ACTUALIZACIÓN
-- =====================================================

-- Actualizar un empleado
SELECT 'ACTUALIZACIÓN DE EMPLEADO:' as titulo;
SELECT * FROM actualizar_empleado(1, 'Juan Carlos', 'Pérez', 'García', 16000.00, '2020-01-15');

-- Verificar la actualización
SELECT 'EMPLEADO DESPUÉS DE ACTUALIZACIÓN:' as titulo;
SELECT * FROM obtener_empleado_por_id(1);

-- =====================================================
-- PRUEBAS DE ELIMINACIÓN
-- =====================================================

-- Eliminar un empleado (descomenta si quieres probar)
-- SELECT 'ELIMINACIÓN DE EMPLEADO:' as titulo;
-- SELECT * FROM eliminar_empleado(5);

-- Verificar la eliminación
-- SELECT 'EMPLEADOS DESPUÉS DE ELIMINACIÓN:' as titulo;
-- SELECT * FROM obtener_empleados();

-- =====================================================
-- RESUMEN FINAL
-- =====================================================

SELECT 'RESUMEN FINAL - TODOS LOS EMPLEADOS CON DETALLES:' as titulo;
SELECT 
    id,
    CONCAT(nombre, ' ', apellido_paterno, ' ', apellido_materno) as nombre_completo,
    sueldo_base,
    fecha_ingreso,
    anos_antiguedad,
    dias_vacaciones,
    aguinaldo
FROM obtener_todos_empleados_detalles()
ORDER BY id;
