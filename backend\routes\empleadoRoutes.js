const express = require('express');
const EmpleadoController = require('../controllers/empleadoController');

const router = express.Router();

// Rutas para empleados
router.get('/', EmpleadoController.obtenerTodos);                    // GET /empleados
router.get('/detalles/todos', EmpleadoController.obtenerTodosConDetalles); // GET /empleados/detalles/todos
router.get('/:id', EmpleadoController.obtenerPorId);                 // GET /empleados/:id
router.get('/:id/detalles', EmpleadoController.obtenerDetalles);     // GET /empleados/:id/detalles
router.post('/', EmpleadoController.crear);                          // POST /empleados
router.put('/:id', EmpleadoController.actualizar);                   // PUT /empleados/:id
router.delete('/:id', EmpleadoController.eliminar);                  // DELETE /empleados/:id

module.exports = router;
