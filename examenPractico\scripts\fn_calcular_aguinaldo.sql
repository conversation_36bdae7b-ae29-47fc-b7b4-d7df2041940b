-- Función para calcular aguinaldo basado en antigüedad
CREATE OR REPLACE FUNCTION fn_calcular_aguinaldo(fecha_ingreso_param DATE, sueldo_base_param NUMERIC)
RETURNS NUMERIC AS $$
DECLARE
    meses_antiguedad INTEGER;
    anos_antiguedad INTEGER;
    aguinaldo NUMERIC;
BEGIN
    -- Calcular meses y años de antigüedad
    meses_antiguedad := EXTRACT(MONTH FROM AGE(CURRENT_DATE, fecha_ingreso_param)) + 
                       (EXTRACT(YEAR FROM AGE(CURRENT_DATE, fecha_ingreso_param)) * 12);
    anos_antiguedad := EXTRACT(YEAR FROM AGE(CURRENT_DATE, fecha_ingreso_param));
    
    -- Calcular aguinaldo según las reglas
    IF anos_antiguedad >= 2 THEN
        -- 2 años cumplidos: 30 días de salario
        aguinaldo := sueldo_base_param;
    ELSIF anos_antiguedad >= 1 THEN
        -- 1 año cumplido: 15 días de salario
        aguinaldo := sueldo_base_param * 0.5;
    ELSIF meses_antiguedad >= 6 THEN
        -- 6 meses cumplidos: 10% proporcional del sueldo base
        aguinaldo := sueldo_base_param * 0.1;
    ELSE
        aguinaldo := 0;
    END IF;
    
    RETURN ROUND(aguinaldo, 2);
END;
$$ LANGUAGE plpgsql;