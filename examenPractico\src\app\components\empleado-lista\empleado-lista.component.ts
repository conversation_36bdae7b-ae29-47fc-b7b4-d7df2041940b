import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EmpleadoService, EmpleadoDetalle } from '../../services/empleado.service';

@Component({
  selector: 'app-empleado-lista',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './empleado-lista.component.html',
  styleUrls: ['./empleado-lista.component.css']
})
export class EmpleadoListaComponent implements OnInit {
  empleados: EmpleadoDetalle[] = [];
  mostrarFormulario = false;
  cargando = false;
  eliminando: number | null = null;
  mensaje = '';
  mensajeExito = true;

  empleadoForm = {
    nombre: '',
    apellido_paterno: '',
    apellido_materno: '',
    sueldo_base: 0,
    fecha_ingreso: ''
  };

  constructor(private empleadoService: EmpleadoService) {}

  ngOnInit() {
    this.cargarEmpleados();
  }

  cargarEmpleados() {
    this.cargando = true;
    this.empleadoService.obtenerTodosConDetalles().subscribe({
      next: (response) => {
        this.empleados = response.data;
        this.cargando = false;
      },
      error: (error) => {
        this.mostrarMensaje('Error al cargar empleados', false);
        this.cargando = false;
      }
    });
  }

  guardarEmpleado() {
    this.empleadoService.crearEmpleado(this.empleadoForm).subscribe({
      next: (response) => {
        this.mostrarMensaje('Empleado creado correctamente', true);
        this.cargarEmpleados();
        this.cancelarFormulario();
      },
      error: (error) => {
        this.mostrarMensaje('Error al crear empleado', false);
      }
    });
  }

  eliminarEmpleado(id: number) {
    if (confirm('¿Estás seguro de que quieres eliminar este empleado?')) {
      this.eliminando = id;
      this.empleadoService.eliminarEmpleado(id).subscribe({
        next: (response) => {
          this.mostrarMensaje('Empleado eliminado correctamente', true);
          this.cargarEmpleados();
          this.eliminando = null;
        },
        error: (error) => {
          this.mostrarMensaje('Error al eliminar empleado', false);
          this.eliminando = null;
        }
      });
    }
  }

  cancelarFormulario() {
    this.mostrarFormulario = false;
    this.empleadoForm = {
      nombre: '',
      apellido_paterno: '',
      apellido_materno: '',
      sueldo_base: 0,
      fecha_ingreso: ''
    };
  }

  mostrarMensaje(mensaje: string, exito: boolean) {
    this.mensaje = mensaje;
    this.mensajeExito = exito;
    setTimeout(() => {
      this.mensaje = '';
    }, 5000);
  }
}
