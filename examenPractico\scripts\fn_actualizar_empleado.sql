-- Actualizar empleado
CREATE OR REPLACE FUNCTION fn_actualizar_empleado(
    empleado_id INTEGER,
    nombre_param VARCHAR(100),
    apellido_paterno_param VARCHAR(100),
    apellido_materno_param VARCHAR(100),
    sueldo_base_param NUMERIC(10,2),
    fecha_ingreso_param DATE
)
RETURNS TABLE(
    id INTEGER,
    nombre VARCHAR(100),
    apellido_paterno VARCHAR(100),
    apellido_materno VARCHAR(100),
    sueldo_base NUMERIC(10,2),
    fecha_ingreso DATE,
    mensaje VARCHAR(100)
) AS $$
BEGIN
    UPDATE empleados 
    SET nombre = nombre_param,
        apellido_paterno = apellido_paterno_param,
        apellido_materno = apellido_materno_param,
        sueldo_base = sueldo_base_param,
        fecha_ingreso = fecha_ingreso_param
    WHERE empleados.id = empleado_id;
    
    IF FOUND THEN
        RETURN QUERY
        SELECT e.id, e.nombre, e.apellido_paterno, e.apellido_materno, e.sueldo_base, e.fecha_ingreso,
            'Empleado actualizado exitosamente'::VARCHAR(100) as mensaje
        FROM empleados e
        WHERE e.id = empleado_id;
    ELSE
        RETURN QUERY
        SELECT NULL::INTEGER, NULL::VARCHAR(100), NULL::VARCHAR(100), NULL::VARCHAR(100), 
            NULL::NUMERIC(10,2), NULL::DATE, 'Empleado no encontrado'::VARCHAR(100);
    END IF;
END;
$$ LANGUAGE plpgsql;