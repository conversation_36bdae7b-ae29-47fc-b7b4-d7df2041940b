-- Obtener todos los empleados con detalles
CREATE OR REPLACE FUNCTION fn_obtener_todos_empleados_detalles()
RETURNS TABLE(
    id INTEGER,
    nombre VARCHAR(100),
    apellido_paterno VARCHAR(100),
    apellido_materno VARCHAR(100),
    sueldo_base NUMERIC(10,2),
    fecha_ingreso DATE,
    anos_antiguedad INTEGER,
    dias_vacaciones INTEGER,
    aguinaldo NUMERIC(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT e.id, 
        e.nombre, 
        e.apellido_paterno, 
        e.apellido_materno, 
        e.sueldo_base, 
        e.fecha_ingreso,
        EXTRACT(YEAR FROM AGE(CURRENT_DATE, e.fecha_ingreso))::INTEGER as anos_antiguedad,
        fn_calcular_dias_vacaciones(e.fecha_ingreso) as dias_vacaciones,
        fn_calcular_aguinaldo(e.fecha_ingreso, e.sueldo_base) as aguinaldo
    FROM empleados e
    ORDER BY e.id;
END;
$$ LANGUAGE plpgsql;
