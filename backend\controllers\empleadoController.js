const EmpleadoModel = require('../models/empleadoModel');

class EmpleadoController {

  // GET /empleados - Obtener todos los empleados
  static async obtenerTodos(req, res) {
    try {
      const empleados = await EmpleadoModel.obtenerTodos();
      res.json({
        success: true,
        data: empleados,
        message: 'Empleados obtenidos correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /empleados/:id - Obtener empleado por ID
  static async obtenerPorId(req, res) {
    try {
      const { id } = req.params;
      const empleado = await EmpleadoModel.obtenerPorId(parseInt(id));
      
      if (!empleado) {
        return res.status(404).json({
          success: false,
          message: 'Empleado no encontrado'
        });
      }

      res.json({
        success: true,
        data: empleado,
        message: 'Empleado obtenido correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // POST /empleados - Crear nuevo empleado
  static async crear(req, res) {
    try {
      const { nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso } = req.body;

      // Validaciones básicas
      if (!nombre || !apellido_paterno || !apellido_materno || !sueldo_base || !fecha_ingreso) {
        return res.status(400).json({
          success: false,
          message: 'Todos los campos son requeridos'
        });
      }

      if (sueldo_base <= 0) {
        return res.status(400).json({
          success: false,
          message: 'El sueldo base debe ser mayor a 0'
        });
      }

      const empleadoData = {
        nombre: nombre.trim(),
        apellido_paterno: apellido_paterno.trim(),
        apellido_materno: apellido_materno.trim(),
        sueldo_base: parseFloat(sueldo_base),
        fecha_ingreso
      };

      const nuevoEmpleado = await EmpleadoModel.crear(empleadoData);
      
      res.status(201).json({
        success: true,
        data: nuevoEmpleado,
        message: 'Empleado creado correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // PUT /empleados/:id - Actualizar empleado
  static async actualizar(req, res) {
    try {
      const { id } = req.params;
      const { nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso } = req.body;

      // Validaciones básicas
      if (!nombre || !apellido_paterno || !apellido_materno || !sueldo_base || !fecha_ingreso) {
        return res.status(400).json({
          success: false,
          message: 'Todos los campos son requeridos'
        });
      }

      if (sueldo_base <= 0) {
        return res.status(400).json({
          success: false,
          message: 'El sueldo base debe ser mayor a 0'
        });
      }

      const empleadoData = {
        nombre: nombre.trim(),
        apellido_paterno: apellido_paterno.trim(),
        apellido_materno: apellido_materno.trim(),
        sueldo_base: parseFloat(sueldo_base),
        fecha_ingreso
      };

      const empleadoActualizado = await EmpleadoModel.actualizar(parseInt(id), empleadoData);
      
      if (!empleadoActualizado || empleadoActualizado.mensaje === 'Empleado no encontrado') {
        return res.status(404).json({
          success: false,
          message: 'Empleado no encontrado'
        });
      }

      res.json({
        success: true,
        data: empleadoActualizado,
        message: 'Empleado actualizado correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // DELETE /empleados/:id - Eliminar empleado
  static async eliminar(req, res) {
    try {
      const { id } = req.params;
      const resultado = await EmpleadoModel.eliminar(parseInt(id));
      
      if (resultado.mensaje === 'Empleado no encontrado') {
        return res.status(404).json({
          success: false,
          message: 'Empleado no encontrado'
        });
      }

      res.json({
        success: true,
        message: 'Empleado eliminado correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /empleados/:id/detalles - Obtener empleado con vacaciones y aguinaldo
  static async obtenerDetalles(req, res) {
    try {
      const { id } = req.params;
      const empleado = await EmpleadoModel.obtenerDetalles(parseInt(id));
      
      if (!empleado) {
        return res.status(404).json({
          success: false,
          message: 'Empleado no encontrado'
        });
      }

      res.json({
        success: true,
        data: empleado,
        message: 'Detalles del empleado obtenidos correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  // GET /empleados/detalles/todos - Obtener todos los empleados con detalles
  static async obtenerTodosConDetalles(req, res) {
    try {
      const empleados = await EmpleadoModel.obtenerTodosConDetalles();
      res.json({
        success: true,
        data: empleados,
        message: 'Empleados con detalles obtenidos correctamente'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

module.exports = EmpleadoController;
