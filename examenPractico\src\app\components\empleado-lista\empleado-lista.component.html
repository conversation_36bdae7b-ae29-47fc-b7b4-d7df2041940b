<div class="container">
  <div class="header">
    <h2>👥 Sistema de Empleados Coppel</h2>
    <div class="actions">
      <input type="text" [(ngModel)]="filtro" (input)="filtrarEmpleados()" placeholder="🔍 Buscar empleado..." class="search-input">
      <button class="btn-primary" (click)="mostrarFormulario = !mostrarFormulario">
        {{ mostrarFormulario ? '❌ Cancelar' : '➕ Agregar' }}
      </button>
      <button class="btn-secondary" (click)="cargarEmpleados()">🔄 Actualizar</button>
    </div>
  </div>

  <!-- Formulario para nuevo/editar empleado -->
  <div *ngIf="mostrarFormulario" class="form-container">
    <h3>{{ empleadoEditando ? '✏️ Editar Empleado' : '➕ Nuevo Empleado' }}</h3>
    <form (ngSubmit)="guardarEmpleado()" #empleadoFormRef="ngForm">
      <div class="form-grid">
        <input type="text" [(ngModel)]="empleadoForm.nombre" name="nombre" placeholder="Nombre" required>
        <input type="text" [(ngModel)]="empleadoForm.apellido_paterno" name="apellido_paterno" placeholder="Apellido Paterno" required>
        <input type="text" [(ngModel)]="empleadoForm.apellido_materno" name="apellido_materno" placeholder="Apellido Materno" required>
        <input type="number" [(ngModel)]="empleadoForm.sueldo_base" name="sueldo_base" placeholder="Sueldo Base" required min="1">
        <input type="date" [(ngModel)]="empleadoForm.fecha_ingreso" name="fecha_ingreso" required>
        <div class="form-buttons">
          <button type="submit" class="btn-success" [disabled]="!empleadoFormRef.form.valid">
            {{ empleadoEditando ? '💾 Actualizar' : '💾 Guardar' }}
          </button>
          <button type="button" class="btn-secondary" (click)="cancelarFormulario()">❌ Cancelar</button>
        </div>
      </div>
    </form>
  </div>

  <!-- Mensaje de estado -->
  <div *ngIf="mensaje" class="mensaje" [ngClass]="{'success': mensajeExito, 'error': !mensajeExito}">
    {{ mensaje }}
  </div>

  <!-- Loading -->
  <div *ngIf="cargando" class="loading">⏳ Cargando empleados...</div>

  <!-- Tabla de empleados -->
  <div *ngIf="!cargando" class="table-container">
    <table class="empleados-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Nombre Completo</th>
          <th>Sueldo Base</th>
          <th>Fecha Ingreso</th>
          <th>Antigüedad</th>
          <th>Vacaciones</th>
          <th>Aguinaldo</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let empleado of empleadosFiltrados" [class.highlight]="empleado.id === empleadoEditando?.id">
          <td>{{ empleado.id }}</td>
          <td>{{ empleado.nombre }} {{ empleado.apellido_paterno }} {{ empleado.apellido_materno }}</td>
          <td>${{ empleado.sueldo_base | number:'1.2-2' }}</td>
          <td>{{ empleado.fecha_ingreso | date:'dd/MM/yyyy' }}</td>
          <td>{{ empleado.anos_antiguedad }} años</td>
          <td>{{ empleado.dias_vacaciones }} días</td>
          <td>${{ empleado.aguinaldo | number:'1.2-2' }}</td>
          <td class="actions-cell">
            <button class="btn-edit" (click)="editarEmpleado(empleado)" title="Editar">✏️</button>
            <button class="btn-delete" (click)="eliminarEmpleado(empleado.id!)"
                    [disabled]="eliminando === empleado.id" title="Eliminar">
              {{ eliminando === empleado.id ? '⏳' : '🗑️' }}
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Mensaje cuando no hay empleados -->
  <div *ngIf="!cargando && empleados.length === 0" class="no-empleados">
    <p>📭 No hay empleados registrados</p>
    <button class="btn-primary" (click)="mostrarFormulario = true">➕ Agregar primer empleado</button>
  </div>

  <!-- Mensaje cuando no hay resultados de búsqueda -->
  <div *ngIf="!cargando && empleados.length > 0 && empleadosFiltrados.length === 0" class="no-results">
    <p>🔍 No se encontraron empleados que coincidan con "{{ filtro }}"</p>
    <button class="btn-secondary" (click)="limpiarFiltro()">Limpiar búsqueda</button>
  </div>
</div>
