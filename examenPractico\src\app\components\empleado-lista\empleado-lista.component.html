<div class="container">
  <div class="header">
    <h2>👥 Emple<PERSON><PERSON></h2>
    <button class="btn-primary" (click)="mostrarFormulario = !mostrarFormulario">
      {{ mostrarFormulario ? '❌ Cancelar' : '➕ Nuevo Empleado' }}
    </button>
  </div>

  <!-- Formulario para nuevo empleado -->
  <div *ngIf="mostrarFormulario" class="form-container">
    <h3>➕ Nuevo Empleado</h3>
    <form (ngSubmit)="guardarEmpleado()" #empleadoFormRef="ngForm">
      <div class="form-row">
        <div class="form-group">
          <label>Nombre:</label>
          <input type="text" [(ngModel)]="empleadoForm.nombre" name="nombre" required>
        </div>
        <div class="form-group">
          <label>Apellido Paterno:</label>
          <input type="text" [(ngModel)]="empleadoForm.apellido_paterno" name="apellido_paterno" required>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label>Apellido Materno:</label>
          <input type="text" [(ngModel)]="empleadoForm.apellido_materno" name="apellido_materno" required>
        </div>
        <div class="form-group">
          <label>Sueldo Base:</label>
          <input type="number" [(ngModel)]="empleadoForm.sueldo_base" name="sueldo_base" required min="1">
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label>Fecha de Ingreso:</label>
          <input type="date" [(ngModel)]="empleadoForm.fecha_ingreso" name="fecha_ingreso" required>
        </div>
      </div>
      <div class="form-actions">
        <button type="submit" class="btn-success" [disabled]="!empleadoFormRef.form.valid">
          💾 Guardar
        </button>
        <button type="button" class="btn-secondary" (click)="cancelarFormulario()">
          ❌ Cancelar
        </button>
      </div>
    </form>
  </div>

  <!-- Mensaje de estado -->
  <div *ngIf="mensaje" class="mensaje" [ngClass]="{'success': mensajeExito, 'error': !mensajeExito}">
    {{ mensaje }}
  </div>

  <!-- Loading -->
  <div *ngIf="cargando" class="loading">
    ⏳ Cargando empleados...
  </div>

  <!-- Lista de empleados -->
  <div *ngIf="!cargando" class="empleados-grid">
    <div *ngFor="let empleado of empleados" class="empleado-card">
      <div class="empleado-info">
        <h3>{{ empleado.nombre }} {{ empleado.apellido_paterno }} {{ empleado.apellido_materno }}</h3>
        <p><strong>💰 Sueldo:</strong> ${{ empleado.sueldo_base | number:'1.2-2' }}</p>
        <p><strong>📅 Ingreso:</strong> {{ empleado.fecha_ingreso | date:'dd/MM/yyyy' }}</p>
        <p><strong>⏰ Antigüedad:</strong> {{ empleado.anos_antiguedad }} años</p>
        <p><strong>🏖️ Vacaciones:</strong> {{ empleado.dias_vacaciones }} días</p>
        <p><strong>🎁 Aguinaldo:</strong> ${{ empleado.aguinaldo | number:'1.2-2' }}</p>
      </div>
      <div class="empleado-actions">
        <button class="btn-delete" (click)="eliminarEmpleado(empleado.id!)"
                [disabled]="eliminando === empleado.id">
          {{ eliminando === empleado.id ? '⏳' : '🗑️ Eliminar' }}
        </button>
      </div>
    </div>
  </div>

  <!-- Mensaje cuando no hay empleados -->
  <div *ngIf="!cargando && empleados.length === 0" class="no-empleados">
    <p>📭 No hay empleados registrados</p>
    <button class="btn-primary" (click)="mostrarFormulario = true">➕ Agregar primer empleado</button>
  </div>
</div>
