import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Empleado {
  id?: number;
  nombre: string;
  apellido_paterno: string;
  apellido_materno: string;
  sueldo_base: number;
  fecha_ingreso: string;
}

export interface EmpleadoDetalle extends Empleado {
  anos_antiguedad: number;
  dias_vacaciones: number;
  aguinaldo: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class EmpleadoService {
  private apiUrl = 'http://localhost:3001/api/empleados';

  constructor(private http: HttpClient) { }

  // Obtener todos los empleados
  obtenerEmpleados(): Observable<ApiResponse<Empleado[]>> {
    return this.http.get<ApiResponse<Empleado[]>>(this.apiUrl);
  }

  // Obtener empleado por ID
  obtenerEmpleado(id: number): Observable<ApiResponse<Empleado>> {
    return this.http.get<ApiResponse<Empleado>>(`${this.apiUrl}/${id}`);
  }

  // Crear empleado
  crearEmpleado(empleado: Empleado): Observable<ApiResponse<Empleado>> {
    return this.http.post<ApiResponse<Empleado>>(this.apiUrl, empleado);
  }

  // Actualizar empleado
  actualizarEmpleado(id: number, empleado: Empleado): Observable<ApiResponse<Empleado>> {
    return this.http.put<ApiResponse<Empleado>>(`${this.apiUrl}/${id}`, empleado);
  }

  // Eliminar empleado
  eliminarEmpleado(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`);
  }

  // Obtener empleado con detalles
  obtenerEmpleadoDetalles(id: number): Observable<ApiResponse<EmpleadoDetalle>> {
    return this.http.get<ApiResponse<EmpleadoDetalle>>(`${this.apiUrl}/${id}/detalles`);
  }

  // Obtener todos los empleados con detalles
  obtenerTodosConDetalles(): Observable<ApiResponse<EmpleadoDetalle[]>> {
    return this.http.get<ApiResponse<EmpleadoDetalle[]>>(`${this.apiUrl}/detalles/todos`);
  }
}
