-- Crear empleado
CREATE OR REPLACE FUNCTION fn_crear_empleado(
    nombre_param VARCHAR(100),
    apellido_paterno_param VARCHAR(100),
    apellido_materno_param VARCHAR(100),
    sueldo_base_param NUMERIC(10,2),
    fecha_ingreso_param DATE
)
RETURNS TABLE(
    id INTEGER,
    nombre VARCHAR(100),
    apellido_paterno VARCHAR(100),
    apellido_materno VARCHAR(100),
    sueldo_base NUMERIC(10,2),
    fecha_ingreso DATE,
    mensaje VARCHAR(100)
) AS $$
BEGIN
    INSERT INTO empleados (nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso)
    VALUES (nombre_param, apellido_paterno_param, apellido_materno_param, sueldo_base_param, fecha_ingreso_param);
    
    RETURN QUERY
    SELECT e.id, e.nombre, e.apellido_paterno, e.apellido_materno, e.sueldo_base, e.fecha_ingreso, 
        'Empleado creado exitosamente'::VARCHAR(100) as mensaje
    FROM empleados e
    WHERE e.id = currval('empleados_id_seq');
END;
$$ LANGUAGE plpgsql;