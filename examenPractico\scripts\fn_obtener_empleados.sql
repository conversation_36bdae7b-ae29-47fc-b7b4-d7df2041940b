-- Obtener todos los empleados
CREATE OR REPLACE FUNCTION fn_obtener_empleados()
RETURNS TABLE(
    id INTEGER,
    nombre VARCHAR(100),
    apellido_paterno VARCHAR(100),
    apellido_materno VARCHAR(100),
    sueldo_base NUMERIC(10,2),
    fecha_ingreso DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT e.id, e.nombre, e.apellido_paterno, e.apellido_materno, e.sueldo_base, e.fecha_ingreso
    FROM empleados e
    ORDER BY e.id;
END;
$$ LANGUAGE plpgsql;